import React from 'react';
import { Card } from 'antd';

const BookingDayCards = ({
    weekData,
    selectedSlots,
    availableSlots,
    handleSlotSelection,
}) => {
    const getGeneratedSlots = () => {
        // Check if availableSlots is valid and contains the 'slots' property
        if (availableSlots && Array.isArray(availableSlots.slots)) {
            return availableSlots.slots.map((slot) => ({
                ...slot,
                value: slot.label,
            }));
        }

        // If availableSlots is not valid or 'slots' does not exist, return an empty array or default data
        return [];
    };

    if (!weekData.days || weekData.days.length === 0) {
        return <div>No days available for selected week</div>;
    }

    return (
        <Card className="wy-booking-scroll-card">
            <div className="wy-booking-scroll-container">
                {weekData?.days.map((day) => (
                    <div key={day.date} className="wy-booking-day-card">
                        <div className="wy-booking-day-header">
                            {day.dayName} <br />
                            {day.displayDate}
                        </div>

                        <div className="wy-slot-grid">
                            {getGeneratedSlots()?.map((slot) => {
                                const isSelected = selectedSlots[
                                    day.date
                                ]?.includes(slot.value);

                                return (
                                    <div
                                        key={`${day.date}-${slot.value}`}
                                        onClick={() =>
                                            handleSlotSelection(
                                                day.date,
                                                slot.value
                                            )
                                        }
                                        className={`wy-slot-box ${
                                            isSelected
                                                ? 'wy-slot-box-selected'
                                                : ''
                                        }`}
                                    >
                                        <div>{slot.label || slot.value}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                ))}
            </div>
        </Card>
    );
};

export default BookingDayCards;
