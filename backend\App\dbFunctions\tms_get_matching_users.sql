-- DROP FUNCTION public.tms_get_matching_users(json);

CREATE OR REPLACE FUNCTION public.tms_get_matching_users(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
 	message text;
 	resp_data json;
 	filter_proto json;
 	org_id_ integer;
 	query_ text;
 	column_name_ text;
 	-- Temp
 	results json ;
 
begin
	status = false;
	message = 'Internal_error';
	resp_data = '{}'::json;

	--form data 
	org_id_      = json_extract_path_text(form_data,'org_id');
--	column_name_ = json_extract_path_text(form_data,'column_name');
	query_ = '%' || json_extract_path_text(form_data,'query') || '%';

--	raise notice 'column_name_ - %',  column_name_;

	resp_data = array_to_json(array(
	   select jsonb_build_object(
			        'value', users.usr_id,
			        'label', 
			        case 
			            when users.designation is null or users.designation = '' 
			            then users."name" || ' - ' || org.nickname
			            else users."name" || ' (' || users.designation || ') - ' ||  '(' || org.nickname  || ')'
			        end
		       ) 
	     from cl_tx_users users
	    inner join public.cl_tx_orgs org 
		   on users.org_id = org.org_id
	    where LOWER(users."name") like LOWER(query_)
	      and users.is_active is true
		  and org.org_type <> 'ORG_TYPE_OWNER'
	    group 
	       by users.usr_id, org.nickname
	));


   	status = true;
	message = 'success';

   	return json_build_object('status',status,'code',message,'data',resp_data);

END;
$function$
;
