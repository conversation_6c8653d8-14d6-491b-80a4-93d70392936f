name: Deploy to Env Dev5
on:
    workflow_dispatch: # Putting here is also fine!!
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    notify-slack-1:
        # needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev5/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp5-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp5-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E7TFYZF2HENSY --paths "/*"
    Preparing-DB-for-Dev5-ENV:
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS 'tmsdev5' WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN Dev DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank Dev DB temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database 'tmsdev5' ;'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdev5'
                  backup_file: 'backups/backup.sql'

    Preparing-Redis-for-Dev5:
        #needs: Preparing-DB-for-Dev5-ENV
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "11b20d17-1bb1-41d4-8bf2-efb235e55c95"

    Deploying-Dockers:
        needs: [Preparing-Redis-for-Dev5, Preparing-DB-for-Dev5-ENV]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev5/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev5/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev5/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev5/Auth'
                  flags: --recursive
    Start-Association:
        needs:
            [
                Deploying-Dockers,
                Preparing-Redis-for-Dev5,
                Preparing-DB-for-Dev5-ENV,
            ]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "76de4e13-4994-45d7-87c4-c93bae98996b"
    notify-slack:
        needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
