-- Create function to get capacity data from cl_tx_capacity table
CREATE OR REPLACE FUNCTION public.tms_ace_get_capacity_by_resource(
    p_resource_id text,
    p_start_date text DEFAULT NULL,
    p_end_date text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql AS $function$
DECLARE
    status boolean;
    message text;
    resp_data json;
    capacity_records json;
    start_date_parsed date;
    end_date_parsed date;
BEGIN
    -- Initialize default values
    status = false;
    message = 'Internal_error';
    
    -- Validate input parameters
    IF p_resource_id IS NULL OR p_resource_id = '' THEN
        message = 'resource_id_required';
        resp_data = json_build_object(
            'status', 'error',
            'message', 'Resource ID is required',
            'timestamp', now()
        );
        RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    END IF;
    
    -- Parse and validate dates
    -- Handle start_date
    IF p_start_date IS NOT NULL AND p_start_date != '' THEN
        -- Validate start_date format
        IF p_start_date !~ '^\d{4}-\d{2}-\d{2}$' THEN
            message = 'invalid_start_date_format';
            resp_data = json_build_object(
                'status', 'error',
                'message', 'Invalid start date format. Use YYYY-MM-DD format',
                'timestamp', now()
            );
            RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
        END IF;
        start_date_parsed = p_start_date::date;
    ELSE
        -- Default to tomorrow if not provided
        start_date_parsed = (CURRENT_DATE + INTERVAL '1 day')::date;
    END IF;

    -- Handle end_date
    IF p_end_date IS NOT NULL AND p_end_date != '' THEN
        -- Validate end_date format
        IF p_end_date !~ '^\d{4}-\d{2}-\d{2}$' THEN
            message = 'invalid_end_date_format';
            resp_data = json_build_object(
                'status', 'error',
                'message', 'Invalid end date format. Use YYYY-MM-DD format',
                'timestamp', now()
            );
            RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
        END IF;
        end_date_parsed = p_end_date::date;
    ELSE
        -- Default to day after start_date if not provided
        end_date_parsed = (start_date_parsed + INTERVAL '1 day')::date;
    END IF;
    
    -- Validate date range
    IF end_date_parsed < start_date_parsed THEN
        message = 'invalid_date_range';
        resp_data = json_build_object(
            'status', 'error',
            'message', 'End date cannot be before start date',
            'timestamp', now()
        );
        RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    END IF;
    
    -- Query capacity data from cl_tx_capacity table
    SELECT json_agg(
               json_build_object(
                   'resourceId', cap.resource_id,
                   'capacityId',cap.db_id ,
                   'resourceLabel', cap.resource_label,
                   'startTime', cap.start_time,
                   'endTime', cap.end_time,
                   'totalCapacity', cap.total_capacity,
                   'availableCapacity', cap.available_capacity,
                   'bookedCapacity', cap.booked_cap_in_minutes,
                   'date', cap.usr_tmzone_day
               ) ORDER BY cap.start_time ASC
           )
      INTO capacity_records
      FROM public.cl_tx_capacity cap
     WHERE cap.resource_id = p_resource_id
       AND ( 
            cap.usr_tmzone_day = start_date_parsed
            OR cap.usr_tmzone_day = end_date_parsed
           );
    
    -- Handle case when no records found
    IF capacity_records IS NULL THEN
        capacity_records = '[]'::json;
    END IF;
    
    -- Build successful response
    status = true;
    message = 'success';
    resp_data = json_build_object(
        'status', 'success',
        'message', 'Capacity data retrieved successfully',
        'data', capacity_records,
        'timestamp', now(),
        'source', 'database',
        'query_params', json_build_object(
            'resource_id', p_resource_id,
            'start_date', start_date_parsed,
            'end_date', end_date_parsed
        )
    );
    
    -- Return in the standard format
    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    
EXCEPTION WHEN OTHERS THEN
    -- Handle any unexpected errors
    message = 'database_error';
    resp_data = json_build_object(
        'status', 'error',
        'message', 'Database error: ' || SQLERRM,
        'timestamp', now()
    );
    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$;
