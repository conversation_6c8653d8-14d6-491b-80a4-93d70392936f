name: QA5v2
on:
    workflow_dispatch: # Putting here is also fine!!
        inputs:
            action:
                type: choice
                description: 'QA05 or RefreshQA05'
                options:
                    - QA05
                    - RefreshQA05
                    - WithProdDB
                required: true
                default: 'QA05'

env:
    PGPASSWORD: ${{ secrets.dev_db_cred_1 }}

jobs:
    notify-slack-1:
        if: ${{ github.event.inputs.action == 'QA05' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    PreparingFrontEnd:
        if: ${{ github.event.inputs.action == 'QA05' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18

            - name: Print npm version
              run: |
                  echo "npm version: $(npm -v)"

            - name: List files in the repository
              run: |
                  ls -a
                  echo node --version
                  echo "npm version: $(npm -v)"
                  echo "Node.js version: $(node -v)"

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/qa05/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.test
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.test
                  rm -rf .env
                  cp .env.test .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-uat
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://tmsqa5/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://tmsqa5 --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E14W2UJZIY0FET --paths "/*"
    Preparing-DB-for-QA05-ENV:
        if: ${{ github.event.inputs.action == 'QA05' }}
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms_uat -U postgres -c 'DROP DATABASE IF EXISTS 'tmsqa5' WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN  DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://postgres:${{ secrets.dev_db_cred_1 }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms_uat'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank  DB temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U postgres -c 'create database 'tmsqa5' ;'

            - name: Postgres Backup Restore from Origin  to new DB  temp
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://postgres:${{ secrets.dev_db_cred_1 }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsqa5'
                  backup_file: 'backups/backup.sql'
    Preparing-Redis-for-QA05:
        if: ${{ github.event.inputs.action == 'QA05' }}
        #needs: Preparing-DB-for-QA02-ENV
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "7e2ce2a4-84a2-4265-837f-a7f4bd72b66f"
    Deploying-Dockers:
        if: ${{ github.event.inputs.action == 'QA05' }}
        needs: [Preparing-Redis-for-QA05, Preparing-DB-for-QA05-ENV]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/Auth'
                  flags: --recursive
    Start-Association:
        if: ${{ github.event.inputs.action == 'QA05' }}
        needs:
            [
                Deploying-Dockers,
                Preparing-Redis-for-QA05,
                Preparing-DB-for-QA05-ENV,
            ]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "d24cf025-8460-4a01-8f4f-fc2666e441d0"
    notify-slack:
        if: ${{ github.event.inputs.action == 'QA05' }}
        needs: [Start-Association, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    PreparingFrontEndRef:
        if: ${{ github.event.inputs.action == 'RefreshQA05' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/qa05/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.test
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.test
                  rm -rf .env
                  cp .env.test .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-uat
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://tmsqa5/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://tmsqa5 --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E14W2UJZIY0FET --paths "/*"
    Deploying-DockersRef:
        if: ${{ github.event.inputs.action == 'RefreshQA05' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/Auth'
                  flags: --recursive
    Start-Association2:
        if: ${{ github.event.inputs.action == 'RefreshQA05' }}
        needs: [Deploying-DockersRef]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "d24cf025-8460-4a01-8f4f-fc2666e441d0"
    notify-slackRef:
        if: ${{ github.event.inputs.action == 'RefreshQA05' }}
        needs: [Deploying-DockersRef, PreparingFrontEndRef]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    PreparingFrontEndProdDB:
        if: ${{ github.event.inputs.action == 'WithProdDB' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/qa05/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.test
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.test
                  rm -rf .env
                  cp .env.test .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-uat
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://tmsqa5/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://tmsqa5 --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E14W2UJZIY0FET --paths "/*"
    Deploying-DockersProdDB:
        if: ${{ github.event.inputs.action == 'WithProdDB' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/qa05/Auth'
                  flags: --recursive
    Start-Association3:
        if: ${{ github.event.inputs.action == 'WithProdDB' }}
        needs: [Deploying-DockersProdDB]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "5042a3b6-9d09-4f6d-b7af-48eff1fa5a36"

    summary-report:
        runs-on: ubuntu-latest
        needs: [notify-slack, notify-slackRef, notify-slackRef] # Modify based on workflow completion
        if: always() # Ensures the summary runs even if some jobs fail
        steps:
            - name: Generate Dynamic Job Summary
              run: |
                  echo "## 🚀 Deployment Summary for ${{ github.event.inputs.action }}" >> $GITHUB_STEP_SUMMARY
                  echo "- **Workflow Name:** ${{ github.workflow }}" >> $GITHUB_STEP_SUMMARY
                  echo "- **Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
                  echo "- **Branch:** ${GITHUB_REF#refs/heads/}" >> $GITHUB_STEP_SUMMARY
                  echo "" >> $GITHUB_STEP_SUMMARY

                  if [[ "${{ github.event.inputs.action }}" == "QA05" ]]; then
                    echo "### ✅ QA05 Deployment Details" >> $GITHUB_STEP_SUMMARY
                    echo "- Frontend Prepared: ✅" >> $GITHUB_STEP_SUMMARY
                    echo "- Database Restored: ✅" >> $GITHUB_STEP_SUMMARY
                    echo "- Redis Configured: ✅" >> $GITHUB_STEP_SUMMARY
                    echo "- Docker Deployed: ✅" >> $GITHUB_STEP_SUMMARY
                  fi

                  if [[ "${{ github.event.inputs.action }}" == "RefreshQA05" ]]; then
                    echo "### 🔄 RefreshQA05 Deployment Details" >> $GITHUB_STEP_SUMMARY
                    echo "- Frontend Refreshed: ✅" >> $GITHUB_STEP_SUMMARY
                    echo "- Docker Redeployed: ✅" >> $GITHUB_STEP_SUMMARY
                  fi

                  if [[ "${{ github.event.inputs.action }}" == "WithProdDB" ]]; then
                    echo "### 🏗 WithProdDB Deployment Details" >> $GITHUB_STEP_SUMMARY
                    echo "- Frontend Built with Prod DB: ✅" >> $GITHUB_STEP_SUMMARY
                    echo "- Docker Deployed with Prod DB: ✅" >> $GITHUB_STEP_SUMMARY
                  fi

                  echo "" >> $GITHUB_STEP_SUMMARY
                  echo "### 📝 Job Status Report" >> $GITHUB_STEP_SUMMARY
                  echo "| Job Name | Status |" >> $GITHUB_STEP_SUMMARY
                  echo "|----------|--------|" >> $GITHUB_STEP_SUMMARY
                  echo "| PreparingFrontEnd | ${{ job.status }} |" >> $GITHUB_STEP_SUMMARY
                  echo "| Deploying-Dockers | ${{ job.status }} |" >> $GITHUB_STEP_SUMMARY
                  echo "| Start-Association | ${{ job.status }} |" >> $GITHUB_STEP_SUMMARY
