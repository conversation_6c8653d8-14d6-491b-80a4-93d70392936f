name: Deploy to Env Dev6
on:
    workflow_dispatch: # Putting here is also fine!!
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    notify-slack-1:
        # needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18

            # - uses: keithweaver/aws-s3-github-action@v1.0.0
            #   with:
            #       command: cp
            #       source: s3://wifytmsnodemodules/yarn.lock
            #       destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
            #       aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            #       aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            #       aws_region: ap-south-1

            # - name: Set Permissions for Yarn Lock File
            #   run: |
            #       sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev6/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1
            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp6-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Generate unique merge ID
              id: generate-merge-id
              run: echo "version_id=$(uuidgen)" >> $GITHUB_ENV

            - name: List files in the repository
              run: |
                  ls -a build/

            - name: Update app_version.json
              run: |
                  echo "{ \"version_id\" : \"$version_id\" }" > build/app_version.json
              env:
                  version_id: ${{ env.version_id }}

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp6-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E2T1N0YHKNKG9M --paths "/*"

    Frontend-Tests:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18

            # - uses: keithweaver/aws-s3-github-action@v1.0.0
            #   with:
            #       command: cp
            #       source: s3://wifytmsnodemodules/yarn.lock
            #       destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
            #       aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            #       aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            #       aws_region: ap-south-1

            # - name: Set Permissions for Yarn Lock File
            #   run: |
            #       sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Run tests
              run: yarn test
              env:
                  CI: true
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

    Preparing-DB-for-Dev6-ENV:
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS 'tmsdev6' WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN Dev DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank Dev DB temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database 'tmsdev6' ;'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdev6'
                  backup_file: 'backups/backup.sql'

    Preparing-Redis-for-Dev6:
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "35d67d13-aceb-402c-abf3-575247eda7e3"

    Deploying-Dockers:
        needs: [Preparing-Redis-for-Dev6, Preparing-DB-for-Dev6-ENV]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev6/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev6/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev6/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev6/Auth'
                  flags: --recursive

            # - name: multiple command
            #   uses: appleboy/ssh-action@v0.1.7
            #   with:
            #     host: ${{ secrets.lowerenv_server }}
            #     username: ubuntu
            #     password: ${{ secrets.lowerenv_server_cred }}
            #     port: 3344
            #     script: |
            #       cd /home/<USER>/dev6
            #       rm -rf /home/<USER>/dev6/App
            #       rm -rf /home/<USER>/dev6/Auth
            #       aws s3 cp s3://wifybuildspec/tmsdevdockers/tmsdev6/App/ /home/<USER>/dev6/App/ --recursive
            #       aws s3 cp s3://wifybuildspec/tmsdevdockers/tmsdev6/Auth/ /home/<USER>/dev6/Auth/ --recursive
            #       aws ssm get-parameter --region=ap-south-1  --name /tms/be/dev6/.env.dev  --output text --query Parameter.Value > /home/<USER>/dev6/App/.env.dev
            #       aws ssm get-parameter --region=ap-south-1  --name /tms/be/auth/dev6/.env.dev  --output text --query Parameter.Value > /home/<USER>/dev6/Auth/.env.dev
            #       aws ssm get-parameter --region=ap-south-1  --name /tms/backend/dev6/Dockerfile  --output text --query Parameter.Value > /home/<USER>/dev6/App/Dockerfile
            #       aws ssm get-parameter --region=ap-south-1  --name /tms/backend/dev6/auth/Dockerfile  --output text --query Parameter.Value > /home/<USER>/dev6/Auth/Dockerfile
            #       cp /home/<USER>/validation.js /home/<USER>/dev6/App
            #       echo "Stopping dev6 dockers running"
            #       docker kill tms-dev6-app6
            #       docker kill tms-dev6-auth6
            #       docker kill redis6
            #       echo "removing old docker image"
            #       docker rmi -f tms-dev6-app6
            #       docker rmi -f tms-dev6-auth6
            #       docker rmi -f redis
            #       echo "Building fresh docker image"
            #       cd /home/<USER>/dev6/App
            #       docker build -t tms-dev6-app6 .
            #       cd /home/<USER>/dev6/Auth
            #       docker build -t tms-dev6-auth6 .
            #       echo "Running new docker image"
            #       cd /home/<USER>/dev6/
            #       docker build -t redis6 .
            #       docker rm `docker ps --no-trunc -aq`
            #       #dont forget to change in redis.conf in folder
            #       docker run --name redis6  --network dev6 -d -p 3489:6379 redis6
            #       sleep 2
            #       docker run -d -v /home/<USER>/logs:/logs --restart unless-stopped  -p 4662:4662 --cpus=".5"  --network dev6 --name tms-dev6-app6 tms-dev6-app6
            #       docker run -d --restart unless-stopped  -p 3552:3552  --network dev6 --name tms-dev6-auth6 tms-dev6-auth6
            #       echo "Success"

    Start-Association:
        needs:
            [
                Deploying-Dockers,
                Preparing-Redis-for-Dev6,
                Preparing-DB-for-Dev6-ENV,
            ]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "9326c265-798e-4d2b-94bd-dcaf303036df"

    notify-slack:
        needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
