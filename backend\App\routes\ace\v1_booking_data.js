var express = require('express');
const { getUserContextFrmReq } = require('../../api_models/utils/authrizor');
var router = express.Router();

//get capacity data
router.get('/capacity', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getCapacityData(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/availability-slots', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getAvailabilitySlots(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

const setParamsToModel = (req) => {
    const booking_data_model =
        require('../../api_models/ace/booking_data').getInstance();
    booking_data_model.database = req.app.get('db');
    booking_data_model.databaseReplica = req.app.get('db_replica');
    booking_data_model.databaseDump = req.app.get('db_dump');
    booking_data_model.ip_addr = req.ip;
    booking_data_model.user_agent = req.get('User-Agent');
    booking_data_model.user_context = getUserContextFrmReq(req);
    return booking_data_model.getFreshInstance(booking_data_model);
};

module.exports = router;
